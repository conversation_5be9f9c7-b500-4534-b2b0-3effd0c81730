{"version": "2.0.0", "tasks": [{"label": "Launch Http Server", "type": "shell", "command": "node_modules/.bin/http-server --cors --port 5002 -a 127.0.0.1 -c-1", "isBackground": true, "problemMatcher": {"pattern": {"regexp": "does not support problems"}, "background": {"activeOnStart": true, "beginsPattern": "Shutting down http-server (will never match)", "endsPattern": "Starting up http-server"}}, "dependsOn": ["npm: watch"]}, {"type": "npm", "script": "watch", "group": "build", "problemMatcher": ["$tsc-watch"], "isBackground": true, "label": "npm: watch"}]}