monaco.languages.registerDocumentSymbolProvider("json", {
	provideDocumentSymbols: function (model, token) {
		return [
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "File",
				kind: 0,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Module",
				kind: 1,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Namespace",
				kind: 2,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Package",
				kind: 3,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Class",
				kind: 4,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Method",
				kind: 5,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Property",
				kind: 6,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Field",
				kind: 7,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Constructor",
				kind: 8,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Enum",
				kind: 9,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Interface",
				kind: 10,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Function",
				kind: 11,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Variable",
				kind: 12,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Constant",
				kind: 13,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "String",
				kind: 14,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Number",
				kind: 15,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Boolean",
				kind: 16,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Array",
				kind: 17,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Object",
				kind: 18,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Key",
				kind: 19,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Null",
				kind: 20,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "EnumMember",
				kind: 21,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Struct",
				kind: 22,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Event",
				kind: 23,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "Operator",
				kind: 24,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
			{
				range: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
				name: "TypeParameter",
				kind: 25,
				detail: "",
				tags: [],
				selectionRange: {
					startLineNumber: 1,
					startColumn: 1,
					endLineNumber: 2,
					endColumn: 1,
				},
			},
		];
	},
});

// press Ctrl+Shift+O to show the symbols in the editor
monaco.editor.create(document.getElementById("container"), {
	value: '{\n\t"dependencies": {\n\t\t\n\t}\n}\n',
	language: "json",
});
