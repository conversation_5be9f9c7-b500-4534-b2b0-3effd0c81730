@base: #f938ab;

.box-shadow(@style, @c) when (iscolor(@c)) {
	border-radius: @style @c;
}

.box-shadow(@style, @alpha: 50%) when (isnumber(@alpha)) {
	.box-shadow(@style, rgba(0, 0, 0, @alpha));
}

.box { 
	color: saturate(@base, 5%);
	border-color: lighten(@base, 30%);
	
	div {
		.box-shadow((0 0 5px), 30%);
	}
}

#header {
	h1 {
		font-size: 26px;
		font-weight: bold;
	}
	
	p { font-size: 12px;
		a { text-decoration: none;
			&:hover { border-width: 1px }
		}
	}
}

@the-border: 1px;
@base-color: #111;
@red:        #842210;

#header {
	color: (@base-color * 3);
	border-left: @the-border;
	border-right: (@the-border * 2);
}

#footer {
	color: (@base-color + #003300);
	border-color: desaturate(@red, 10%);
}
