name: CI

on: [push, pull_request]

jobs:
  build:
    name: CI
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@1f8c6b94b26d0feae1e387ca63ccbdc44d27b561 # pin@v2
        with:
          node-version: 20

      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-cacheNodeModules2-${{ hashFiles('**/package-lock.json', '**/package.json') }}
          restore-keys: ${{ runner.os }}-cacheNodeModules2-

      - name: execute `npm ci` (1)
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: npm ci

      - name: execute `npm ci` (2)
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: npm ci --prefix webpack-plugin

      - name: Install Playwright
        run: npm run playwright-install

      - name: Install OS Dependencies for Playwright
        run: sudo npm run playwright-install-deps

      - name: Check prettier
        run: npm run prettier-check

      - name: Build
        run: npm run build-monaco-editor

      - name: Run unit tests
        run: npm test

      - name: Compile webpack plugin
        run: npm run compile --prefix webpack-plugin

      - name: Package using webpack plugin
        run: npm run package-for-smoketest-webpack

      - name: Package using esbuild
        run: npm run package-for-smoketest-esbuild

      - name: Package using vite
        run: npm run package-for-smoketest-vite

      #      - name: Package using parcel
      #        run: npm run package-for-smoketest-parcel --prefix test/smoke/parcel
      # Disabled for now, as the parcel bundler cannot deal with VS Code process variable

      - name: Run smoke test
        run: npm run smoketest

      - name: Install website node modules
        working-directory: website
        run: yarn install --frozen-lockfile

      - name: Build website
        working-directory: website
        run: yarn run build

      - name: Test website
        working-directory: website
        run: yarn test
