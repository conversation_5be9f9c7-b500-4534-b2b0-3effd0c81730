# Monaco Editor Webpack Loader Plugin

A plugin to simplify loading the [Monaco Editor](https://github.com/microsoft/monaco-editor) with [webpack](https://webpack.js.org/).

## Installing

```sh
npm install monaco-editor-webpack-plugin
```

## Using

- `webpack.config.js`:

```js
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const path = require('path');

module.exports = {
	entry: './index.js',
	output: {
		path: path.resolve(__dirname, 'dist'),
		filename: 'app.js'
	},
	module: {
		rules: [
			{
				test: /\.css$/,
				use: ['style-loader', 'css-loader']
			},
			{
				test: /\.ttf$/,
				type: 'asset/resource'
			}
		]
	},
	plugins: [new MonacoWebpackPlugin()]
};
```

If using Webpack 4 or lower, it is necessary to use the file-loader instead of Asset Modules like the code below:

```js
{
	test: /\.ttf$/,
	use: ['file-loader']
}
```

- `index.js`:

```js
import * as monaco from 'monaco-editor';
// or import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
// if shipping only a subset of the features & languages is desired

monaco.editor.create(document.getElementById('container'), {
	value: 'console.log("Hello, world")',
	language: 'javascript'
});
```

## Options

Options can be passed in to `MonacoWebpackPlugin`. They can be used to generate a smaller editor bundle by selecting only certain languages or only certain editor features:

- `filename` (`string`) - custom filename template for worker scripts, respects the same options as [loader-utils' interpolateName](https://github.com/webpack/loader-utils#interpolatename). Useful for adding content-based hashes so that files can be served with long-lived caching headers.
  - default value: `'[name].worker.js'`.
- `publicPath` (`string`) - custom public path for worker scripts, overrides the public path from which files generated by this plugin will be served. This wins out over Webpack's dynamic runtime path and can be useful to avoid attempting to load workers cross-origin when using a CDN for other static resources. Use e.g. '/' if you want to load your resources from the current origin..
  - default value: `''`.
- `languages` (`string[]`) - include only a subset of the languages supported. By default, all languages shipped with the `monaco-editor` will be included.

  Some languages share the same web worker. If one of the following languages is included, you must also include the language responsible for instantiating their shared worker:

  | Language   | Instantiator |
  | ---------- | ------------ |
  | javascript | typescript   |
  | handlebars | html         |
  | scss, less | css          |

  To view a list of all available languages, you can run `import metadata from 'monaco-editor/esm/metadata'; console.log(metadata.languages);`.

- `features` (`string[]`) - include only a subset of the editor features. By default, all features shipped with the `monaco-editor` will be included. Instead of enumerating included features, it is also possible to exclude certain default features prefixing them with an exclamation mark '!'.

  To view a list of all available features, you can run `import metadata from 'monaco-editor/esm/metadata'; console.log(metadata.features);`.

- `globalAPI` (`boolean`) - specify whether the editor API should be exposed through a global `monaco` object or not. This option is applicable to `0.22.0` and newer version of `monaco-editor`. Since `0.22.0`, the ESM version of the monaco editor does no longer define a global `monaco` object unless `global.MonacoEnvironment = { globalAPI: true }` is set ([change log](https://github.com/microsoft/monaco-editor/blob/main/CHANGELOG.md#0220-29012021)).
  - default value: `false`.

## Version Matrix

| `monaco-editor-webpack-plugin` | `monaco-editor`                        |
| ------------------------------ | -------------------------------------- |
| `7.*.*`                        | `>= 0.31.0`                            |
| `6.*.*`                        | `0.30.*`                               |
| `5.*.*`                        | `0.29.*`                               |
| `4.*.*`                        | `0.25.*`, `0.26.*`, `0.27.*`, `0.28.*` |
| `3.*.*`                        | `0.22.*`, `0.23.*`, `0.24.*`           |
| `2.*.*`                        | `0.21.*`                               |
| `1.9.*`                        | `0.20.*`                               |
| `1.8.*`                        | `0.19.*`                               |
| `1.7.*`                        | `0.18.*`                               |

## Contributing

This project welcomes contributions and suggestions. Most contributions require you to agree to a
Contributor License Agreement (CLA) declaring that you have the right to, and actually do, grant us
the rights to use your contribution. For details, visit https://cla.microsoft.com.

When you submit a pull request, a CLA-bot will automatically determine whether you need to provide
a CLA and decorate the PR appropriately (e.g., label, comment). Simply follow the instructions
provided by the bot. You will only need to do this once across all repos using our CLA.

This project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/).
For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or
contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.
