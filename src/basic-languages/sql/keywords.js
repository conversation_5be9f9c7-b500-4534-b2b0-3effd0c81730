/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

const keywordsSet = new Set();

addArrToSet(keywordsSet, getMicrosoftSQLKeywords());
addArrToSet(keywordsSet, getSQLiteKeywords());
addArrToSet(keywordsSet, getSnowflakeSQLKeywords());

const keywords = setToArr(keywordsSet);
keywords.sort();
console.log(`'${keywords.join("',\n'")}'`);

function addArrToSet(set, arr) {
	for (const el of arr) {
		set.add(el);
	}
}

function setToArr(set) {
	const arr = [];
	for (const el of set) {
		arr.push(el);
	}
	return arr;
}

function getMicrosoftSQLKeywords() {
	// https://docs.microsoft.com/en-us/sql/t-sql/language-elements/reserved-keywords-transact-sql?view=sql-server-ver15
	return `
		ADD
		EXTERNAL
		PROCEDURE
		ALL
		FETCH
		PUBLIC
		ALTER
		FILE
		RAISERROR
		AND
		FILLFACTOR
		READ
		ANY
		FOR
		READTEXT
		AS
		FOREIGN
		RECONFIGURE
		ASC
		FREETEXT
		REFERENCES
		AUTHORIZATION
		FREETEXTTABLE
		REPLICATION
		BACKUP
		FROM
		RESTORE
		BEGIN
		FULL
		RESTRICT
		BETWEEN
		FUNCTION
		RETURN
		BREAK
		GOTO
		REVERT
		BROWSE
		GRANT
		REVOKE
		BULK
		GROUP
		RIGHT
		BY
		HAVING
		ROLLBACK
		CASCADE
		HOLDLOCK
		ROWCOUNT
		CASE
		IDENTITY
		ROWGUIDCOL
		CHECK
		IDENTITY_INSERT
		RULE
		CHECKPOINT
		IDENTITYCOL
		SAVE
		CLOSE
		IF
		SCHEMA
		CLUSTERED
		IN
		SECURITYAUDIT
		COALESCE
		INDEX
		SELECT
		COLLATE
		INNER
		SEMANTICKEYPHRASETABLE
		COLUMN
		INSERT
		SEMANTICSIMILARITYDETAILSTABLE
		COMMIT
		INTERSECT
		SEMANTICSIMILARITYTABLE
		COMPUTE
		INTO
		SESSION_USER
		CONSTRAINT
		IS
		SET
		CONTAINS
		JOIN
		SETUSER
		CONTAINSTABLE
		KEY
		SHUTDOWN
		CONTINUE
		KILL
		SOME
		CONVERT
		LEFT
		STATISTICS
		CREATE
		LIKE
		SYSTEM_USER
		CROSS
		LINENO
		TABLE
		CURRENT
		LOAD
		TABLESAMPLE
		CURRENT_DATE
		MERGE
		TEXTSIZE
		CURRENT_TIME
		NATIONAL
		THEN
		CURRENT_TIMESTAMP
		NOCHECK
		TO
		CURRENT_USER
		NONCLUSTERED
		TOP
		CURSOR
		NOT
		TRAN
		DATABASE
		NULL
		TRANSACTION
		DBCC
		NULLIF
		TRIGGER
		DEALLOCATE
		OF
		TRUNCATE
		DECLARE
		OFF
		TRY_CONVERT
		DEFAULT
		OFFSETS
		TSEQUAL
		DELETE
		ON
		UNION
		DENY
		OPEN
		UNIQUE
		DESC
		OPENDATASOURCE
		UNPIVOT
		DISK
		OPENQUERY
		UPDATE
		DISTINCT
		OPENROWSET
		UPDATETEXT
		DISTRIBUTED
		OPENXML
		USE
		DOUBLE
		OPTION
		USER
		DROP
		OR
		VALUES
		DUMP
		ORDER
		VARYING
		ELSE
		OUTER
		VIEW
		END
		OVER
		WAITFOR
		ERRLVL
		PERCENT
		WHEN
		ESCAPE
		PIVOT
		WHERE
		EXCEPT
		PLAN
		WHILE
		EXEC
		PRECISION
		WITH
		EXECUTE
		PRIMARY
		WITHIN GROUP
		EXISTS
		PRINT
		WRITETEXT
		EXIT
		PROC
		ABSOLUTE
		EXEC
		OVERLAPS
		ACTION
		EXECUTE
		PAD
		ADA
		EXISTS
		PARTIAL
		ADD
		EXTERNAL
		PASCAL
		ALL
		EXTRACT
		POSITION
		ALLOCATE
		FALSE
		PRECISION
		ALTER
		FETCH
		PREPARE
		AND
		FIRST
		PRESERVE
		ANY
		FLOAT
		PRIMARY
		ARE
		FOR
		PRIOR
		AS
		FOREIGN
		PRIVILEGES
		ASC
		FORTRAN
		PROCEDURE
		ASSERTION
		FOUND
		PUBLIC
		AT
		FROM
		READ
		AUTHORIZATION
		FULL
		REAL
		AVG
		GET
		REFERENCES
		BEGIN
		GLOBAL
		RELATIVE
		BETWEEN
		GO
		RESTRICT
		BIT
		GOTO
		REVOKE
		BIT_LENGTH
		GRANT
		RIGHT
		BOTH
		GROUP
		ROLLBACK
		BY
		HAVING
		ROWS
		CASCADE
		HOUR
		SCHEMA
		CASCADED
		IDENTITY
		SCROLL
		CASE
		IMMEDIATE
		SECOND
		CAST
		IN
		SECTION
		CATALOG
		INCLUDE
		SELECT
		CHAR
		INDEX
		SESSION
		CHAR_LENGTH
		INDICATOR
		SESSION_USER
		CHARACTER
		INITIALLY
		SET
		CHARACTER_LENGTH
		INNER
		SIZE
		CHECK
		INPUT
		SMALLINT
		CLOSE
		INSENSITIVE
		SOME
		COALESCE
		INSERT
		SPACE
		COLLATE
		INT
		SQL
		COLLATION
		INTEGER
		SQLCA
		COLUMN
		INTERSECT
		SQLCODE
		COMMIT
		INTERVAL
		SQLERROR
		CONNECT
		INTO
		SQLSTATE
		CONNECTION
		IS
		SQLWARNING
		CONSTRAINT
		ISOLATION
		SUBSTRING
		CONSTRAINTS
		JOIN
		SUM
		CONTINUE
		KEY
		SYSTEM_USER
		CONVERT
		LANGUAGE
		TABLE
		CORRESPONDING
		LAST
		TEMPORARY
		COUNT
		LEADING
		THEN
		CREATE
		LEFT
		TIME
		CROSS
		LEVEL
		TIMESTAMP
		CURRENT
		LIKE
		TIMEZONE_HOUR
		CURRENT_DATE
		LOCAL
		TIMEZONE_MINUTE
		CURRENT_TIME
		LOWER
		TO
		CURRENT_TIMESTAMP
		MATCH
		TRAILING
		CURRENT_USER
		MAX
		TRANSACTION
		CURSOR
		MIN
		TRANSLATE
		DATE
		MINUTE
		TRANSLATION
		DAY
		MODULE
		TRIM
		DEALLOCATE
		MONTH
		TRUE
		DEC
		NAMES
		UNION
		DECIMAL
		NATIONAL
		UNIQUE
		DECLARE
		NATURAL
		UNKNOWN
		DEFAULT
		NCHAR
		UPDATE
		DEFERRABLE
		NEXT
		UPPER
		DEFERRED
		NO
		USAGE
		DELETE
		NONE
		USER
		DESC
		NOT
		USING
		DESCRIBE
		NULL
		VALUE
		DESCRIPTOR
		NULLIF
		VALUES
		DIAGNOSTICS
		NUMERIC
		VARCHAR
		DISCONNECT
		OCTET_LENGTH
		VARYING
		DISTINCT
		OF
		VIEW
		DOMAIN
		ON
		WHEN
		DOUBLE
		ONLY
		WHENEVER
		DROP
		OPEN
		WHERE
		ELSE
		OPTION
		WITH
		END
		OR
		WORK
		END-EXEC
		ORDER
		WRITE
		ESCAPE
		OUTER
		YEAR
		EXCEPT
		OUTPUT
		ZONE
		EXCEPTION
	`
		.split(/\r\n|\r|\n/)
		.map((t) => t.trim())
		.filter((t) => !!t);
}

function getSQLiteKeywords() {
	// https://www.sqlite.org/lang_keywords.html
	return `
		ABORT
		ACTION
		ADD
		AFTER
		ALL
		ALTER
		ALWAYS
		ANALYZE
		AND
		AS
		ASC
		ATTACH
		AUTOINCREMENT
		BEFORE
		BEGIN
		BETWEEN
		BY
		CASCADE
		CASE
		CAST
		CHECK
		COLLATE
		COLUMN
		COMMIT
		CONFLICT
		CONSTRAINT
		CREATE
		CROSS
		CURRENT
		CURRENT_DATE
		CURRENT_TIME
		CURRENT_TIMESTAMP
		DATABASE
		DEFAULT
		DEFERRABLE
		DEFERRED
		DELETE
		DESC
		DETACH
		DISTINCT
		DO
		DROP
		EACH
		ELSE
		END
		ESCAPE
		EXCEPT
		EXCLUDE
		EXCLUSIVE
		EXISTS
		EXPLAIN
		FAIL
		FILTER
		FIRST
		FOLLOWING
		FOR
		FOREIGN
		FROM
		FULL
		GENERATED
		GLOB
		GROUP
		GROUPS
		HAVING
		IF
		IGNORE
		IMMEDIATE
		IN
		INDEX
		INDEXED
		INITIALLY
		INNER
		INSERT
		INSTEAD
		INTERSECT
		INTO
		IS
		ISNULL
		JOIN
		KEY
		LAST
		LEFT
		LIKE
		LIMIT
		MATCH
		MATERIALIZED
		NATURAL
		NO
		NOT
		NOTHING
		NOTNULL
		NULL
		NULLS
		OF
		OFFSET
		ON
		OR
		ORDER
		OTHERS
		OUTER
		OVER
		PARTITION
		PLAN
		PRAGMA
		PRECEDING
		PRIMARY
		QUERY
		RAISE
		RANGE
		RECURSIVE
		REFERENCES
		REGEXP
		REINDEX
		RELEASE
		RENAME
		REPLACE
		RESTRICT
		RETURNING
		RIGHT
		ROLLBACK
		ROW
		ROWS
		SAVEPOINT
		SELECT
		SET
		TABLE
		TEMP
		TEMPORARY
		THEN
		TIES
		TO
		TRANSACTION
		TRIGGER
		UNBOUNDED
		UNION
		UNIQUE
		UPDATE
		USING
		VACUUM
		VALUES
		VIEW
		VIRTUAL
		WHEN
		WHERE
		WINDOW
		WITH
		WITHOUT
	`
		.split(/\r\n|\r|\n/)
		.map((t) => t.trim())
		.filter((t) => !!t);
}

function getSnowflakeSQLKeywords() {
	// https://docs.snowflake.com/en/sql-reference/reserved-keywords
	return `
		ACCOUNT
		ALL
		ALTER
		AND
		ANY
		AS
		BETWEEN
		BY
		CASE
		CAST
		CHECK
		COLUMN
		CONNECT
		CONNECTION
		CONSTRAINT
		CREATE
		CROSS
		CURRENT
		CURRENT_DATE
		CURRENT_TIME
		CURRENT_TIMESTAMP
		CURRENT_USER
		DATABASE
		DELETE
		DISTINCT
		DROP
		ELSE
		EXISTS
		FALSE
		FOLLOWING
		FOR
		FROM
		FULL
		GRANT
		GROUP
		GSCLUSTER
		HAVING
		ILIKE
		IN
		INCREMENT
		INNER
		INSERT
		INTERSECT
		INTO
		IS
		ISSUE
		JOIN
		LATERAL
		LEFT
		LIKE
		LOCALTIME
		LOCALTIMESTAMP
		MINUS
		NATURAL
		NOT
		NULL
		OF
		ON
		OR
		ORDER
		ORGANIZATION
		QUALIFY
		REGEXP
		REVOKE
		RIGHT
		RLIKE
		ROW
		ROWS
		SAMPLE
		SCHEMA
		SELECT
		SET
		SOME
		START
		TABLE
		TABLESAMPLE
		THEN
		TO
		TRIGGER
		TRUE
		TRY_CAST
		UNION
		UNIQUE
		UPDATE
		USING
		VALUES
		VIEW
		WHEN
		WHENEVER
		WHERE
		WINDOW
		WITH
	`
		.split(/\r\n|\r|\n/)
		.map((t) => t.trim())
		.filter((t) => !!t);
}
