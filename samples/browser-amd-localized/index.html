<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
	</head>
	<body>
		<h2>Monaco Editor Localization Sample</h2>
		<div id="container" style="width: 800px; height: 600px; border: 1px solid grey"></div>

		<script src="../node_modules/monaco-editor/min/vs/loader.js"></script>
		<script>
			require.config({ paths: { vs: '../node_modules/monaco-editor/min/vs' } });

			require.config({
				'vs/nls': {
					availableLanguages: {
						'*': 'de'
					}
				}
			});

			require(['vs/editor/editor.main'], function () {
				var editor = monaco.editor.create(document.getElementById('container'), {
					value: ['function x() {', '\tconsole.log("Hello world!");', '}'].join('\n'),
					language: 'javascript'
				});
			});
		</script>
	</body>
</html>
