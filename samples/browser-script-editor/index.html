<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
		<link
			rel="stylesheet"
			data-name="vs/editor/editor.main"
			href="../node_modules/monaco-editor/min/vs/editor/editor.main.css"
		/>
	</head>
	<body>
		<h2>Monaco Editor Sync Loading Sample</h2>
		<div id="container" style="width: 800px; height: 600px; border: 1px solid grey"></div>

		<script>
			var require = { paths: { vs: '../node_modules/monaco-editor/min/vs' } };
		</script>
		<script src="../node_modules/monaco-editor/min/vs/loader.js"></script>
		<script src="../node_modules/monaco-editor/min/vs/editor/editor.main.js"></script>

		<script>
			var editor = monaco.editor.create(document.getElementById('container'), {
				value: ['function x() {', '\tconsole.log("Hello world!");', '}'].join('\n'),
				language: 'javascript'
			});
		</script>
	</body>
</html>
