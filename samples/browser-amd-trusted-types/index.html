<!DOCTYPE html>
<html>
	<head>
		<title>browser-amd-editor</title>
		<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
		<meta http-equiv="Content-Security-Policy" content="require-trusted-types-for 'script'" />
	</head>
	<body>
		<h2>Monaco Editor with Trusted Types Sample</h2>
		<div id="container" style="width: 800px; height: 600px; border: 1px solid grey"></div>

		<script src="../node_modules/monaco-editor/min/vs/loader.js"></script>
		<script>
			// Allow monaco-editor to load scripts from its own paths only
			var scriptLoadingPolicy = {
				createScriptURL: function allowOnlyMonacoPaths(url) {
					if (
						url.indexOf('../node_modules/monaco-editor/min/vs/') === 0 &&
						url.lastIndexOf('..') == 0
					) {
						return url;
					}
				}
			};
			// If browser supports Trusted Types, use them.
			if (typeof trustedTypes !== 'undefined') {
				scriptLoadingPolicy = trustedTypes.createPolicy('monaco-editor', scriptLoadingPolicy);
			}
			require.config({
				paths: { vs: '../node_modules/monaco-editor/min/vs' },
				trustedTypesPolicy: scriptLoadingPolicy
			});

			require(['vs/editor/editor.main'], function () {
				var editor = monaco.editor.create(document.getElementById('container'), {
					value: ['function x() {', '\tconsole.log("Hello world!");', '}'].join('\n'),
					language: 'javascript'
				});
			});
		</script>
	</body>
</html>
