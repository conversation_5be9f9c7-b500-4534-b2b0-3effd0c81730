<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="772px" height="562px" viewBox="-0.5 -0.5 772 562" content="&lt;mxfile&gt;&lt;diagram id=&quot;cU4r2CM7fKKh95v30pN0&quot; name=&quot;Page-1&quot;&gt;7Vvdc9o4EP9rmLnrDIzlL+AxIaV9aG4yk4e7PgosQFfb4mQRoH/9SZZkbEmEj5hAp3lprJW0lnb399NqTTvBKNt8oXC5eCQJSju+l2w6wUPH96Owz/8Vgq0UxEAJ5hQnUgR2gmf8Eymhp6QrnKCiMZARkjK8bAqnJM/RlDVkkFKybg6bkbT51iWcI0vwPIWpLf0bJ2whpQO/v5N/RXi+0G8G8VD2ZFAPVjspFjAh65oo+NwJRpQQJp+yzQilwnbaLnLeeE9vtTCKcnbMhFAt7AWmK7W5fJmVKqY/hBX8OOWK7ieUP83FU0ZyOCVdlGBGqNoE22rLrBeYoWc+WbTX3Pl8xoJlKW8B/viCKMPcjncpnudcxogYMMNpOiIpVxc85CTnU+/tfaitCQ1oUxOpfX1BJEOMbvmQjQ6qSE5RQQYiZfP1zmV+oMYsau7yh2ogVGEyr3TvLMkflDHLJpn8KwLN91I4QamcJpVoS/u1xSon1OyGEh5gqqlMUDMb2mD2T+35O3/2epFqPYgNe7qx1Y2cr1hM6no9zwNaIqcG/VALdrPLVmP6E6KY7xxRJdzrlYKs6FRtRYGAQTpHatRAisQmX/UcRSlk+KWJMpcX1NQngnO283jlOO1x05FyTWqW4ctqGS738qbysBtGgYUiVkwtcIj9f5PxUfcuVGCYcqsKU1soyXCSCB33FBX4J5yU+oQ/lmIn5d6i+0704PSQvwc3FTsqfQ0CcuGJx5Ef+oov3ugpDU09g8xmBTrRJzaXWU5AxWSFObf73ufnR6c7TsVc/0jQeU28RQfQdi6whkcCS5mdM0EVEKd58I5SuK0NUJG3H4pxE4q+FzXPoQPjA+AZ0SBXYMxuNX6iMyOkFhXfa123ESEAXIR7T40H0Df8O3zFv2f4Ln4F+3ePD7dBxVFbVOz1wjBqZjdRK7ysCUUp7V6CpvuWq/iMgtrH5YFcci8ijk8RB0a+ENsZItCZej1DjNtKEMdkxWXjFObzFc+2OYDGMEu6VfKgbTaobcNOHm8o6Q78wyb1XSYdnG7SQ0lZOGjlyL/iiT+0+TxoPZfeY13LnPZN0Rm/k1XOGZLz7hgVmeUASng3SpQRXIF7gSCsgutQEJ6Ba/sK7lmb/sWiTqcMjTQivFbY6dUcH3eCQTv9+wS9dHyu3stw3unbKcBVQjE4lg9bCUX/Vw/F8F1C8ayMNjIy2ujADccc33YGDOwaxCmVvO6UUGSFyw1lFkauFoaOXK3vABII20CSXVuoSMgoiI5vhXri6DD1gMGFqEenKccY7Gp5gmkhV55wMQuF18wT/FbYOXDkp/rTzqVrvZFR67VKintqvW+lef2eM2nbdQ381Et6cgk1/AxrSzr5xtcCNip+rQp4NjaGDmgEb77HOW/Kfb6ncUWrQkGNWStIgSsbDRjZXjh8L6PZ9HJuNfN2cr/AZpf2b7/nkYJBPlF4IPczxr9a3T6PRKpDtAqA4MbAEAXXA4Oz5ujORmS7RslXz0oczHuxrES/u2EpwwrFAi7F43RF0+095dcLAU+nOWoRtbOdaM1SvPyqnssofiIFZpiIWwSVW6puF9+M/qoGr6v1KZrVx9u1esKgmtsdCs8V8hclkqhacFkUhw2XxcDymOuOXyHkTR5z1UfkVS/BLw3Pxf+tSHkF5C6bl/7gt77ynnYnKHM++cMXXxXECVt/+LM0kjcjOevOYIbTrZzAFcJMeFmpHcFiChMMedeIJMjslvpGJC9IyjesDnJDBed/jDgMvb/Q2q2A45MUZZS1rW+3SxkfYo8ALDeyI8U56mrvlV1D1RVX+uS9Wpufa82d9t/3DlMRK7p5aUivVxa4ys9Z8m/XoC35RvEhpXxpcyEidRKhoKUGnnmQs8t9BoMrRhTehOqCUfIDGeUBR8VA2EiLEjSDq1RbTv0cDLSW6IIGdge+jd2onWPKxq7/m7Kt4xOo5cS9HvONA/Jd2dZVCPpg2yuyrSxy0pWgWkWRvwfrvQ1DoIkhV+ULXIr1IgeGPljv0DlleKwLov5RvBcGcQs+s39g88F7t8V7e1PCDxbch6kANO9tvs2C1e377SWK3a/6ZYln918jgs//Aw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="535" y="130" width="235" height="290" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 233px; height: 1px; padding-top: 137px; margin-left: 536px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                npm package
                                <br/>
                                monaco-editor
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="653" y="149" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    npm package...
                </text>
            </switch>
        </g>
        <path d="M 120 170 L 233.41 169.92" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 238.66 169.92 L 231.66 173.43 L 233.41 169.92 L 231.66 166.43 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 170px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                tsc
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="174" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    tsc
                </text>
            </switch>
        </g>
        <path d="M 120 185 L 170 185 Q 180 185 180 195 L 180 280 Q 180 290 190 290 L 233.63 290" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 238.88 290 L 231.88 293.5 L 233.63 290 L 231.88 286.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 240px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                esbuild ESM
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="243" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    esbuild ESM
                </text>
            </switch>
        </g>
        <path d="M 90 200 L 90 360 Q 90 370 100 370 L 233.63 370" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 238.88 370 L 231.88 373.5 L 233.63 370 L 231.88 366.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 360px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                esbuild AMD
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="181" y="364" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    esbuild AMD
                </text>
            </switch>
        </g>
        <rect x="0" y="140" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 170px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                /src
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="174" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    /src
                </text>
            </switch>
        </g>
        <rect x="240" y="140" width="220" height="80" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 147px; margin-left: 241px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                /out/languages/amd-tsc
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="159" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    /out/languages/amd-tsc
                </text>
            </switch>
        </g>
        <path d="M 460 290 L 563.63 290" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 568.88 290 L 561.88 293.5 L 563.63 290 L 561.88 286.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="240" y="260" width="220" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 290px; margin-left: 241px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                /out/languages/bundled/esm
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="294" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    /out/languages/bundled/esm
                </text>
            </switch>
        </g>
        <path d="M 460 370 L 563.63 370" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 568.88 370 L 561.88 373.5 L 563.63 370 L 561.88 366.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="240" y="340" width="220" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 370px; margin-left: 241px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                /out/languages/bundled/amd-{dev, min}
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="374" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    /out/languages/bundled/amd-{dev, min}
                </text>
            </switch>
        </g>
        <path d="M 130 485 L 480 485 Q 490 485 490 475 L 490 380 Q 490 370 500 370 L 563.63 370" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 568.88 370 L 561.88 373.5 L 563.63 370 L 561.88 366.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="0" y="420" width="170" height="140" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 427px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                npm package
                                <br/>
                                monaco-editor-core
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="85" y="439" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    npm package...
                </text>
            </switch>
        </g>
        <rect x="570" y="340" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 370px; margin-left: 571px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                /out/monaco-editor/{dev, min}
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="374" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    /out/monaco-editor/{dev, min}
                </text>
            </switch>
        </g>
        <rect x="570" y="260" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 290px; margin-left: 571px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                /out/monaco-editor/esm
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="294" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    /out/monaco-editor/esm
                </text>
            </switch>
        </g>
        <path d="M 450 195 L 480 195 Q 490 195 500 195 L 563.63 195" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 568.88 195 L 561.88 198.5 L 563.63 195 L 561.88 191.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="360" y="180" width="90" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 195px; margin-left: 361px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                *.d.ts
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="405" y="199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    *.d.ts
                </text>
            </switch>
        </g>
        <rect x="40" y="470" width="90" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 485px; margin-left: 41px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                {/dev, /min}
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="85" y="489" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    {/dev, /min}
                </text>
            </switch>
        </g>
        <path d="M 130 525 L 500 525 Q 510 525 510 515 L 510 300 Q 510 290 520 290 L 563.63 290" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 568.88 290 L 561.88 293.5 L 563.63 290 L 561.88 286.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="40" y="510" width="90" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 525px; margin-left: 41px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                esm
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="85" y="529" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    esm
                </text>
            </switch>
        </g>
        <rect x="570" y="180" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 210px; margin-left: 571px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                /out/monaco-editor/monaco.d.ts
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="214" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    /out/monaco-editor/monaco.d.ts
                </text>
            </switch>
        </g>
        <path d="M 504 41 L 499 41 Q 494 41 494 51 L 494 91 Q 494 101 489 101 L 486.5 101 Q 484 101 489 101 L 491.5 101 Q 494 101 494 111 L 494 151 Q 494 161 499 161 L 504 161" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(494,0)scale(-1,1)translate(-494,0)rotate(90,494,101)" pointer-events="all"/>
        <rect x="361" y="62" width="250" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 77px; margin-left: 486px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <div style="background-color: rgb(255, 255, 255); font-family: &quot;Cascadia Code&quot;, Consolas, &quot;Courier New&quot;, monospace, Consolas, &quot;Courier New&quot;, monospace; font-size: 11px; line-height: 19px;">
                                    <span style="font-size: 11px;">
                                        ts-node ./build/build-monaco-editor
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="486" y="80" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ts-node ./build/build-monaco-editor
                </text>
            </switch>
        </g>
        <path d="M 190 41 L 185 41 Q 180 41 180 51 L 180 91 Q 180 101 175 101 L 172.5 101 Q 170 101 175 101 L 177.5 101 Q 180 101 180 111 L 180 151 Q 180 161 185 161 L 190 161" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(180,0)scale(-1,1)translate(-180,0)rotate(90,180,101)" pointer-events="all"/>
        <rect x="130" y="60" width="110" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 75px; margin-left: 185px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <div style="background-color: rgb(255, 255, 255); font-family: &quot;Cascadia Code&quot;, Consolas, &quot;Courier New&quot;, monospace, Consolas, &quot;Courier New&quot;, monospace; font-size: 11px; line-height: 19px;">
                                    npm run build
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="185" y="78" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    npm run build
                </text>
            </switch>
        </g>
        <path d="M 350 -177 L 345 -177 Q 340 -177 340 -167 L 340 31 Q 340 41 335 41 L 332.5 41 Q 330 41 335 41 L 337.5 41 Q 340 41 340 51 L 340 249 Q 340 259 345 259 L 350 259" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(340,0)scale(-1,1)translate(-340,0)rotate(90,340,41)" pointer-events="all"/>
        <rect x="234" y="0" width="200" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 15px; margin-left: 334px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <div style="background-color: rgb(255, 255, 255); font-family: &quot;Cascadia Code&quot;, Consolas, &quot;Courier New&quot;, monospace, Consolas, &quot;Courier New&quot;, monospace; font-size: 11px; line-height: 19px;">
                                    npm run build-monaco-editor
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="334" y="18" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    npm run build-monaco-editor
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
