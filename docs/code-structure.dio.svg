<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="601px" height="391px" viewBox="-0.5 -0.5 601 391" content="&lt;mxfile&gt;&lt;diagram id=&quot;cU4r2CM7fKKh95v30pN0&quot; name=&quot;Page-1&quot;&gt;1Vpdk9osFP41XrqThBj1sqvd9qKd6XTfmbaXmKChjSED8au//j0kkA+IdtXo7t6sgcAJPOechwfYAZqt9584zuKvLCLJwHOi/QDNB57nOhMHfmTNoayZ+KpixWmkGtUVz/Qv0T1V7YZGRLQa5owlOc3alSFLUxLmrTrMOdu1my1Z0v5qhlfEqngOcWLX/qBRHqtZeOO6/jOhq1h/2Q2m5Zs11o3VTESMI7ZrVKGPAzTjjOXl03o/I4kET+NS9ns68rYaGCdp/pIOHlLjyA96ciSCuapiylL4eYzzdQIlFx7JnuY/4dl5GKnSr8abuXSxowsHXUhzfmh0ksVfzXd1t6Kk+5VDk+M5OjtVJdiGh7pVoCIC8xXRzcYVthCUhK0JfAfacJLgnG7b9rGKjlXVrgYQHhSG3Xgir7SxxclGWf3G2bYIVxhSTOrByrjjneh/wQtImBbsOKGrFJ5DmDnhULElPKcQkR/UizWNImnjkRNB/+JFYU+CmDGa5sWMRo+D0byCVRog+0FHuqjOdZA2AS8DxkZSWXIeIJnLXiq3FSAvRlqZ/iaHXdsdul7L6tD32ybYcinA26arqhG+yHs6dBre+0Tzz5sF1H0nGRM0Z3KUQQJoPC7Ae8FKPq1pyJlgS/j+01aEwHeWX3cxzclzhoso3QEttv171CdWsB+F3tXuUhAFU1Xe1RTlauaNG/QUONfHvTu2kEuzdTH+8I9kUxszluKQDUkkMR2GjJ+N2ZImyYwlkEQVUZlZkbPsNuBWK9a/wHWdPtCdXh+XLbzPhbob1w4H9AA1mvw7jpHfBbXbA9TazxcG8ivkPZq28XIdryM2gxslPhrdV0B4ZykIkkYfpN6TK2eChaBhWflEk7YHzhUZlexsiIyGgLtMZBRdYbz40Gig1u+jK6M/NphpZCg+sz0y2k8cw9/lCC5dQ5G9EjQUUILT1aZII2dJcL7htSASdmrRNEw2EU1X0Oi/QwYSnNMM5jKTgSxDqngKhSgffguW2kZwGsE7BtqLi85Yfctyq0yvE3ILTYO2MlLGr9VbuovWW1p/9aq39EcasfIEezHCbS+CKcHDV6DXkdPOl3HXyu90sOuoj9XIv5RdG9xaM203u0ZYxCTSBm7GmF4f7GgFanUmoOnM1FslQ6te10WrrcLmRAAlLSSJxYCZPIaAP4sNTaJ3RzVlrJ2gGteZGJsw1AvXGNJ6aBjoh2nsffkppinWGXu1uL+Yuy/bjC9lGyXLzhVz47PE3J14SgnaXo+PLlJ2gdbWmtrMszwzjwLnVPvrlZ29NZqTLVTMSQbIkzQ8vD/SO+LZivTGAGubnrx+SK9tdHIDzkPBxel8h2y+awK/ztYsMBMSnU7gkaFlpqjnBJ5YCaxONR7KrdM7S94ywE9tjnyt2Qxtce3uqG01GN9CsYxsxUKTjvMnMHTSi7cXLYH7iqIF3fkG640cQKE3cwBl7sCm0/MOoIz217OcbyWOdQVXXjc4+sy2PoF6bwx4+jZu6Dw4vtvrfdxNBYt9HCRimmUg+o+76A4Xa+aJadfFWtf5un8+vUGxvvwvIaz/hQJ9/B8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 90 60 L 90 143.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 90 148.88 L 86.5 141.88 L 90 143.63 L 93.5 141.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 74px; margin-left: 80px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Provides the source for
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="77" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Provides the source for
                </text>
            </switch>
        </g>
        <rect x="0" y="0" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 30px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                GitHub Repository
                                <br/>
                                microsoft/vscode
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="34" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    GitHub Repository...
                </text>
            </switch>
        </g>
        <rect x="0" y="150" width="180" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 157px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                npm package
                                <br/>
                                monaco-editor-core
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="169" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    npm package...
                </text>
            </switch>
        </g>
        <rect x="260" y="0" width="340" height="110" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 338px; height: 1px; padding-top: 7px; margin-left: 261px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                GitHub Repository
                                <br/>
                                microsoft/monaco-editor
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    GitHub Repository...
                </text>
            </switch>
        </g>
        <rect x="270" y="330" width="160" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 360px; margin-left: 271px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                npm package
                                <br/>
                                monaco-editor
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="364" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    npm package...
                </text>
            </switch>
        </g>
        <path d="M 430 100 L 358 154 Q 350 160 342 166 L 318 184 Q 310 190 310 200 L 310 323.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 310 328.88 L 306.5 321.88 L 310 323.63 L 313.5 321.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 231px; margin-left: 290px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Provides language feature sources
                                <br/>
                                including TypeScript, html, css, json
                                <br/>
                                and others
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="290" y="234" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Provides language feature sources...
                </text>
            </switch>
        </g>
        <rect x="380" y="50" width="100" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 75px; margin-left: 381px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Folder
                                <br/>
                                /src
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="79" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Folder...
                </text>
            </switch>
        </g>
        <path d="M 270 75 L 96.25 108.78" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 91.1 109.79 L 97.3 105.01 L 96.25 108.78 L 98.64 111.89 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 91px; margin-left: 190px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Describe how to build
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="94" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Describe how to build
                </text>
            </switch>
        </g>
        <rect x="270" y="50" width="100" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 75px; margin-left: 271px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Folder
                                <br/>
                                /scripts
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="320" y="79" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Folder...
                </text>
            </switch>
        </g>
        <path d="M 515 100 L 515 120 Q 515 130 505 130 L 150 130 Q 140 130 138.27 136.91 L 136.54 143.82" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 135.27 148.92 L 133.57 141.28 L 136.54 143.82 L 140.36 142.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Dev Dependency
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="181" y="124" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Dev Dependency
                </text>
            </switch>
        </g>
        <path d="M 540 100 L 540 130 Q 540 140 531.68 145.55 L 398.32 234.45 Q 390 240 390 250 L 390 323.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 390 328.88 L 386.5 321.88 L 390 323.63 L 393.5 321.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 391px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                package.json
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="391" y="303" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    package.json
                </text>
            </switch>
        </g>
        <rect x="490" y="50" width="100" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 75px; margin-left: 491px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                File
                                <br/>
                                /package.json
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="79" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    File...
                </text>
            </switch>
        </g>
        <path d="M 90 240 L 90 290 Q 90 300 100 300 L 300 300 Q 310 300 310 310 L 310 323.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 310 328.88 L 306.5 321.88 L 310 323.63 L 313.5 321.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 290px; margin-left: 179px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Provides the core editor sources
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="179" y="294" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Provides the core editor sources
                </text>
            </switch>
        </g>
        <rect x="10" y="200" width="160" height="40" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 220px; margin-left: 11px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                shipped sources
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="224" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    shipped sources
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
